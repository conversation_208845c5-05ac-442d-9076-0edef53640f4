import React, { useCallback, useMemo, useState } from 'react';
import { cn } from '@/components/core/class-config';
import classConfig from '@/components/core/components/tree/class-config.ts';
import { DefaultCollapseIcon, DefaultExpandIcon } from '@/components/core/components/tree/icons.tsx';
import type { ListProps, ListRef } from '@/components/core/components/list';
import List from '@/components/core/components/list';
import RadioGroup from '@/components/core/components/radio';
import Checkbox from '@/components/core/components/checkbox';

export interface TreeNode {
  /** 节点的唯一标识 */
  key: string | number;
  /** 节点标题 */
  title: React.ReactNode;
  /** 子节点 */
  children?: TreeNode[];
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否可选择 */
  selectable?: boolean;
  /** 是否默认展开 */
  defaultExpanded?: boolean;
}

export type TreeRef = ListRef;

export type TreeProps = {
  /** 树形数据 */
  treeData: TreeNode[];
  /** 是否支持多选 */
  multiple?: boolean;
  /** 是否可选择 */
  selectable?: boolean;
  /** 选中的节点key（单选模式） */
  selectedKey?: string | number;
  /** 选中的节点key（多选模式） */
  selectedKeys?: (string | number)[];
  /** 展开的节点key */
  expandedKeys?: (string | number)[];
  /** 默认展开的节点key */
  defaultExpandedKeys?: (string | number)[];
  /** 默认选中的节点key（单选模式） */
  defaultSelectedKey?: string | number;
  /** 默认选中的节点key（多选模式） */
  defaultSelectedKeys?: (string | number)[];
  /** 是否显示展开/收起图标 */
  showIcon?: boolean;
  /** 自定义展开图标 */
  expandIcon?: React.ReactNode;
  /** 自定义收起图标 */
  collapseIcon?: React.ReactNode;
  /** 节点选择回调（单选模式） */
  onSelect?: (selectedKey: string | number | undefined, info: {
    node: TreeNode;
  }) => void;
  /** 节点选择回调（多选模式） */
  onMultipleSelect?: (selectedKeys: (string | number)[], info: {
    selectedNodes: TreeNode[];
  }) => void;
  /** 展开/收起回调 */
  onExpand?: (expandedKeys: (string | number)[], info: {
    expanded: boolean;
    node: TreeNode;
  }) => void;
  /** 自定义类名 */
  className?: string;
  /** 滚动列表的ref */
  ref?: React.Ref<TreeRef>;
} & Pick<ListProps, 'virtualScroll' | 'infiniteScroll' | 'getPositionCache'>;

interface FlattenNode extends TreeNode {
  level: number;
  parentKey?: string | number;
  isLeaf: boolean;
}

const Tree = React.forwardRef<TreeRef, TreeProps>((props, ref) => {
  const {
    treeData,
    multiple = false,
    selectable = true,
    selectedKey: controlledSelectedKey,
    selectedKeys: controlledSelectedKeys,
    expandedKeys: controlledExpandedKeys,
    defaultExpandedKeys = [],
    defaultSelectedKey,
    defaultSelectedKeys = [],
    showIcon = true,
    expandIcon = <DefaultExpandIcon />,
    collapseIcon = <DefaultCollapseIcon />,
    onSelect,
    onMultipleSelect,
    onExpand,
    className,
    virtualScroll = false,
    infiniteScroll,
    getPositionCache,
  } = props;

  // 内部状态管理
  const [internalSelectedKey, setInternalSelectedKey] = useState<string | number | undefined>(
    controlledSelectedKey || defaultSelectedKey
  );
  const [internalSelectedKeys, setInternalSelectedKeys] = useState<(string | number)[]>(
    controlledSelectedKeys || defaultSelectedKeys
  );
  const [internalExpandedKeys, setInternalExpandedKeys] = useState<(string | number)[]>(
    controlledExpandedKeys || defaultExpandedKeys
  );

  // 使用受控或非受控状态
  const selectedKey = controlledSelectedKey !== undefined ? controlledSelectedKey : internalSelectedKey;
  const selectedKeys = controlledSelectedKeys || internalSelectedKeys;
  const expandedKeys = controlledExpandedKeys || internalExpandedKeys;

  // 扁平化树形数据
  const flattenNodes = useMemo(() => {
    const flatten = (nodes: TreeNode[], level = 0, parentKey?: string | number): FlattenNode[] => {
      const result: FlattenNode[] = [];
      
      nodes.forEach(node => {
        const flatNode: FlattenNode = {
          ...node,
          level,
          parentKey,
          isLeaf: !node.children || node.children.length === 0,
        };
        
        result.push(flatNode);
        
        // 如果节点展开且有子节点，递归处理子节点
        if (expandedKeys.includes(node.key) && node.children) {
          result.push(...flatten(node.children, level + 1, node.key));
        }
      });
      
      return result;
    };
    
    return flatten(treeData);
  }, [treeData, expandedKeys]);

  // 处理单选节点选择
  const handleSingleSelect = useCallback((newSelectedKey: string | number | undefined) => {
    if (controlledSelectedKey === undefined) {
      setInternalSelectedKey(newSelectedKey);
    }

    const selectedNode = flattenNodes.find(n => n.key === newSelectedKey);
    if (selectedNode) {
      onSelect?.(newSelectedKey, { node: selectedNode });
    }
  }, [controlledSelectedKey, flattenNodes, onSelect]);

  // 处理多选节点选择
  const handleMultipleSelect = useCallback((newSelectedKeys: (string | number)[]) => {
    if (!controlledSelectedKeys) {
      setInternalSelectedKeys(newSelectedKeys);
    }

    // 获取选中的节点信息
    const selectedNodes = flattenNodes.filter(n => newSelectedKeys.includes(n.key));

    onMultipleSelect?.(newSelectedKeys, { selectedNodes });
  }, [controlledSelectedKeys, flattenNodes, onMultipleSelect]);

  // 处理展开/收起
  const handleNodeExpand = useCallback((node: FlattenNode) => {
    if (node.isLeaf) return;

    const isExpanded = expandedKeys.includes(node.key);
    let newExpandedKeys: (string | number)[];

    if (isExpanded) {
      newExpandedKeys = expandedKeys.filter(key => key !== node.key);
    } else {
      newExpandedKeys = [...expandedKeys, node.key];
    }

    if (!controlledExpandedKeys) {
      setInternalExpandedKeys(newExpandedKeys);
    }

    onExpand?.(newExpandedKeys, {
      expanded: !isExpanded,
      node,
    });
  }, [expandedKeys, controlledExpandedKeys, onExpand]);

  // 渲染节点
  const renderNode = useCallback((node: FlattenNode) => {
    const isExpanded = expandedKeys.includes(node.key);
    const canExpand = !node.isLeaf;
    const nodeSelectable = selectable && node.selectable !== false && !node.disabled;

    return (
      <div
        key={node.key}
        className={cn(classConfig.nodeConfig)}
        data-disabled={node.disabled}
      >
        {/* 缩进 */}
        <div
          className={classConfig.nodeContentConfig.indent}
          style={{ width: `${node.level * 24}px` }}
        />

        {/* 展开/收起图标 */}
        {showIcon && (
          <div
            className={classConfig.nodeContentConfig.switcher}
            onClick={() => handleNodeExpand(node)}
            data-testid={canExpand ? (isExpanded ? 'collapse-icon' : 'expand-icon') : 'leaf-icon'}
          >
            {canExpand ? (
              isExpanded ? collapseIcon : expandIcon
            ) : null}
          </div>
        )}

        {/* 节点内容 */}
        <div className={classConfig.nodeContentConfig.wrap}>
          {nodeSelectable ? (
            multiple ? (
              <Checkbox
                value={node.key}
                disabled={node.disabled}
              >
                {node.title}
              </Checkbox>
            ) : (
              <RadioGroup.Radio
                value={node.key}
                disabled={node.disabled}
              >
                {node.title}
              </RadioGroup.Radio>
            )
          ) : (
            <div className={classConfig.nodeContentConfig.title}>
              {node.title}
            </div>
          )}
        </div>
      </div>
    );
  }, [expandedKeys, showIcon, expandIcon, collapseIcon, handleNodeExpand, selectable, multiple]);

  const listContent = (
    <List
      ref={ref}
      className={cn(
        classConfig.treeConfig({
          defaultScrollHeight: Boolean(virtualScroll) || !!infiniteScroll,
          isScroll: !virtualScroll && !!infiniteScroll,
        }),
        className,
      )}
      virtualScroll={virtualScroll}
      infiniteScroll={infiniteScroll}
      getPositionCache={getPositionCache}
      list={flattenNodes}
    >
      {(nodes) => nodes.map(renderNode)}
    </List>
  );

  if (!selectable) {
    return listContent;
  }

  if (multiple) {
    return (
      <Checkbox.Group
        value={selectedKeys}
        onChange={handleMultipleSelect}
      >
        {listContent}
      </Checkbox.Group>
    );
  } else {
    return (
      <RadioGroup
        value={selectedKey}
        onChange={handleSingleSelect}
      >
        {listContent}
      </RadioGroup>
    );
  }
});
});

Tree.displayName = 'Tree';

export default Tree;
