import React, { useCallback, useMemo, useState } from 'react';
import { cn } from '@/components/core/class-config';
import classConfig from '@/components/core/components/tree/class-config.ts';
import { DefaultCollapseIcon, DefaultExpandIcon } from '@/components/core/components/tree/icons.tsx';
import type { ListProps, ListRef } from '@/components/core/components/list';
import List from '@/components/core/components/list';
import RadioGroup from '@/components/core/components/radio';
import Checkbox from '@/components/core/components/checkbox';

export interface TreeNode {
  /** 节点的唯一标识 */
  key: string | number;
  /** 节点标题 */
  title: React.ReactNode;
  /** 子节点 */
  children?: TreeNode[];
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否可选择 */
  selectable?: boolean;
  /** 是否默认展开 */
  defaultExpanded?: boolean;
}

interface FlattenNode extends TreeNode {
  level: number;
  parentKey?: string | number;
  isLeaf: boolean;
  childrenKeys: (string | number)[];
  allDescendantKeys: (string | number)[];
}

export type TreeRef = ListRef;

export type TreeProps = {
  /** 树形数据 */
  treeData: TreeNode[];
  /** 是否支持多选 */
  multiple?: boolean;
  /** 是否可选择 */
  selectable?: boolean;
  /** 是否启用父子节点联动（仅多选模式有效） */
  checkStrictly?: boolean;
  /** 选中的节点key（单选模式） */
  selectedKey?: string | number;
  /** 选中的节点key（多选模式） */
  selectedKeys?: (string | number)[];
  /** 展开的节点key */
  expandedKeys?: (string | number)[];
  /** 默认展开的节点key */
  defaultExpandedKeys?: (string | number)[];
  /** 默认选中的节点key（单选模式） */
  defaultSelectedKey?: string | number;
  /** 默认选中的节点key（多选模式） */
  defaultSelectedKeys?: (string | number)[];
  /** 是否显示展开/收起图标 */
  showIcon?: boolean;
  /** 自定义展开图标 */
  expandIcon?: React.ReactNode;
  /** 自定义收起图标 */
  collapseIcon?: React.ReactNode;
  /** 节点选择回调（单选模式） */
  onSelect?: (selectedKey: string | number | undefined, info: {
    node: TreeNode;
  }) => void;
  /** 节点选择回调（多选模式） */
  onMultipleSelect?: (selectedKeys: (string | number)[], info: {
    selectedNodes: TreeNode[];
    checkedKeys: (string | number)[];
    halfCheckedKeys: (string | number)[];
  }) => void;
  /** 展开/收起回调 */
  onExpand?: (expandedKeys: (string | number)[], info: {
    expanded: boolean;
    node: TreeNode;
  }) => void;
  /** 自定义类名 */
  className?: string;
  /** 滚动列表的ref */
  ref?: React.Ref<TreeRef>;
} & Pick<ListProps, 'virtualScroll' | 'infiniteScroll' | 'getPositionCache'>;



const Tree = React.forwardRef<TreeRef, TreeProps>((props, ref) => {
  const {
    treeData,
    multiple = false,
    selectable = true,
    checkStrictly = false,
    selectedKey: controlledSelectedKey,
    selectedKeys: controlledSelectedKeys,
    expandedKeys: controlledExpandedKeys,
    defaultExpandedKeys = [],
    defaultSelectedKey,
    defaultSelectedKeys = [],
    showIcon = true,
    expandIcon = <DefaultExpandIcon />,
    collapseIcon = <DefaultCollapseIcon />,
    onSelect,
    onMultipleSelect,
    onExpand,
    className,
    virtualScroll = false,
    infiniteScroll,
    getPositionCache,
  } = props;

  // 内部状态管理
  const [internalSelectedKey, setInternalSelectedKey] = useState<string | number | undefined>(
    controlledSelectedKey || defaultSelectedKey
  );
  const [internalSelectedKeys, setInternalSelectedKeys] = useState<(string | number)[]>(
    controlledSelectedKeys || defaultSelectedKeys
  );
  const [internalExpandedKeys, setInternalExpandedKeys] = useState<(string | number)[]>(
    controlledExpandedKeys || defaultExpandedKeys
  );

  // 使用受控或非受控状态
  const selectedKey = controlledSelectedKey !== undefined ? controlledSelectedKey : internalSelectedKey;
  const selectedKeys = controlledSelectedKeys || internalSelectedKeys;
  const expandedKeys = controlledExpandedKeys || internalExpandedKeys;

  // 构建节点关系映射
  const nodeMap = useMemo(() => {
    const map = new Map<string | number, TreeNode>();
    const childrenMap = new Map<string | number, (string | number)[]>();
    const descendantMap = new Map<string | number, (string | number)[]>();

    const buildMap = (nodes: TreeNode[], parentKey?: string | number) => {
      nodes.forEach(node => {
        map.set(node.key, node);

        if (parentKey) {
          const siblings = childrenMap.get(parentKey) || [];
          siblings.push(node.key);
          childrenMap.set(parentKey, siblings);
        }

        if (node.children) {
          buildMap(node.children, node.key);

          // 收集所有后代节点
          const descendants: (string | number)[] = [];
          const collectDescendants = (children: TreeNode[]) => {
            children.forEach(child => {
              descendants.push(child.key);
              if (child.children) {
                collectDescendants(child.children);
              }
            });
          };
          collectDescendants(node.children);
          descendantMap.set(node.key, descendants);
        }
      });
    };

    buildMap(treeData);
    return { nodeMap: map, childrenMap, descendantMap };
  }, [treeData]);

  // 扁平化树形数据
  const flattenNodes = useMemo(() => {
    const flatten = (nodes: TreeNode[], level = 0, parentKey?: string | number): FlattenNode[] => {
      const result: FlattenNode[] = [];

      nodes.forEach(node => {
        const childrenKeys = nodeMap.childrenMap.get(node.key) || [];
        const allDescendantKeys = nodeMap.descendantMap.get(node.key) || [];

        const flatNode: FlattenNode = {
          ...node,
          level,
          parentKey,
          isLeaf: !node.children || node.children.length === 0,
          childrenKeys,
          allDescendantKeys,
        };

        result.push(flatNode);

        // 如果节点展开且有子节点，递归处理子节点
        if (expandedKeys.includes(node.key) && node.children) {
          result.push(...flatten(node.children, level + 1, node.key));
        }
      });

      return result;
    };

    return flatten(treeData);
  }, [treeData, expandedKeys, nodeMap]);

  // 处理单选节点选择
  const handleSingleSelect = useCallback((newSelectedKey: string | number | undefined) => {
    if (controlledSelectedKey === undefined) {
      setInternalSelectedKey(newSelectedKey);
    }

    const selectedNode = flattenNodes.find(n => n.key === newSelectedKey);
    if (selectedNode) {
      onSelect?.(newSelectedKey, { node: selectedNode });
    }
  }, [controlledSelectedKey, flattenNodes, onSelect]);

  // 计算半选状态
  const getCheckState = useCallback((nodeKey: string | number, checkedKeys: (string | number)[]) => {
    if (checkStrictly) {
      return {
        checked: checkedKeys.includes(nodeKey),
        indeterminate: false,
      };
    }

    const node = flattenNodes.find(n => n.key === nodeKey);
    if (!node || node.isLeaf) {
      return {
        checked: checkedKeys.includes(nodeKey),
        indeterminate: false,
      };
    }

    const childrenKeys = node.childrenKeys;
    const checkedChildren = childrenKeys.filter(key => checkedKeys.includes(key));

    if (checkedChildren.length === 0) {
      // 检查是否有后代节点被选中
      const hasCheckedDescendants = node.allDescendantKeys.some(key => checkedKeys.includes(key));
      return {
        checked: checkedKeys.includes(nodeKey),
        indeterminate: hasCheckedDescendants,
      };
    } else if (checkedChildren.length === childrenKeys.length) {
      // 所有直接子节点都被选中，检查是否所有后代都被选中
      const allDescendantsChecked = node.allDescendantKeys.every(key => checkedKeys.includes(key));
      return {
        checked: allDescendantsChecked,
        indeterminate: !allDescendantsChecked,
      };
    } else {
      // 部分子节点被选中
      return {
        checked: false,
        indeterminate: true,
      };
    }
  }, [checkStrictly, flattenNodes]);

  // 处理父子节点联动选择
  const getUpdatedKeysWithCascade = useCallback((
    targetKey: string | number,
    checked: boolean,
    currentKeys: (string | number)[]
  ): (string | number)[] => {
    if (checkStrictly) {
      return checked
        ? [...currentKeys, targetKey]
        : currentKeys.filter(key => key !== targetKey);
    }

    let newKeys = [...currentKeys];
    const targetNode = flattenNodes.find(n => n.key === targetKey);

    if (!targetNode) return newKeys;

    if (checked) {
      // 选中节点：选中自己和所有后代
      newKeys.push(targetKey);
      newKeys.push(...targetNode.allDescendantKeys);

      // 检查父节点是否应该被选中
      if (targetNode.parentKey) {
        const parentNode = flattenNodes.find(n => n.key === targetNode.parentKey);
        if (parentNode) {
          const allSiblingsChecked = parentNode.childrenKeys.every(key =>
            newKeys.includes(key) || key === targetKey
          );
          if (allSiblingsChecked) {
            newKeys = getUpdatedKeysWithCascade(targetNode.parentKey, true, newKeys);
          }
        }
      }
    } else {
      // 取消选中：取消自己和所有后代
      newKeys = newKeys.filter(key =>
        key !== targetKey && !targetNode.allDescendantKeys.includes(key)
      );

      // 取消父节点选中
      if (targetNode.parentKey) {
        newKeys = newKeys.filter(key => key !== targetNode.parentKey);
        // 递归取消祖先节点
        let currentParent = targetNode.parentKey;
        while (currentParent) {
          const parentNode = flattenNodes.find(n => n.key === currentParent);
          if (parentNode?.parentKey) {
            newKeys = newKeys.filter(key => key !== parentNode.parentKey);
            currentParent = parentNode.parentKey;
          } else {
            break;
          }
        }
      }
    }

    return [...new Set(newKeys)]; // 去重
  }, [checkStrictly, flattenNodes]);

  // 处理多选节点选择
  const handleMultipleSelect = useCallback((newSelectedKeys: (string | number)[]) => {
    if (!controlledSelectedKeys) {
      setInternalSelectedKeys(newSelectedKeys);
    }

    // 获取选中的节点信息
    const selectedNodes = flattenNodes.filter(n => newSelectedKeys.includes(n.key));

    // 计算半选状态
    const checkedKeys: (string | number)[] = [];
    const halfCheckedKeys: (string | number)[] = [];

    flattenNodes.forEach(node => {
      const { checked, indeterminate } = getCheckState(node.key, newSelectedKeys);
      if (checked) {
        checkedKeys.push(node.key);
      } else if (indeterminate) {
        halfCheckedKeys.push(node.key);
      }
    });

    onMultipleSelect?.(newSelectedKeys, {
      selectedNodes,
      checkedKeys,
      halfCheckedKeys,
    });
  }, [controlledSelectedKeys, flattenNodes, onMultipleSelect, getCheckState]);

  // 处理展开/收起
  const handleNodeExpand = useCallback((node: FlattenNode) => {
    if (node.isLeaf) return;

    const isExpanded = expandedKeys.includes(node.key);
    let newExpandedKeys: (string | number)[];

    if (isExpanded) {
      newExpandedKeys = expandedKeys.filter(key => key !== node.key);
    } else {
      newExpandedKeys = [...expandedKeys, node.key];
    }

    if (!controlledExpandedKeys) {
      setInternalExpandedKeys(newExpandedKeys);
    }

    onExpand?.(newExpandedKeys, {
      expanded: !isExpanded,
      node,
    });
  }, [expandedKeys, controlledExpandedKeys, onExpand]);

  // 渲染节点
  const renderNode = useCallback((node: FlattenNode) => {
    const isExpanded = expandedKeys.includes(node.key);
    const canExpand = !node.isLeaf;
    const nodeSelectable = selectable && node.selectable !== false && !node.disabled;

    // 计算选中和半选状态
    const { checked, indeterminate } = multiple
      ? getCheckState(node.key, selectedKeys)
      : { checked: false, indeterminate: false };

    return (
      <div
        key={node.key}
        className={cn(classConfig.nodeConfig)}
        data-disabled={node.disabled}
      >
        {/* 缩进 */}
        <div
          className={classConfig.nodeContentConfig.indent}
          style={{ width: `${node.level * 24}px` }}
        />

        {/* 展开/收起图标 */}
        {showIcon && (
          <div
            className={classConfig.nodeContentConfig.switcher}
            onClick={() => handleNodeExpand(node)}
            data-testid={canExpand ? (isExpanded ? 'collapse-icon' : 'expand-icon') : 'leaf-icon'}
          >
            {canExpand ? (
              isExpanded ? collapseIcon : expandIcon
            ) : null}
          </div>
        )}

        {/* 节点内容 */}
        <div className={classConfig.nodeContentConfig.wrap}>
          {nodeSelectable ? (
            multiple ? (
              <Checkbox
                value={node.key}
                disabled={node.disabled}
                checked={checked}
                indeterminate={indeterminate}
                onChange={(newChecked) => {
                  const newKeys = getUpdatedKeysWithCascade(node.key, newChecked, selectedKeys);
                  handleMultipleSelect(newKeys);
                }}
              >
                {node.title}
              </Checkbox>
            ) : (
              <RadioGroup.Radio
                value={node.key}
                disabled={node.disabled}
              >
                {node.title}
              </RadioGroup.Radio>
            )
          ) : (
            <div className={classConfig.nodeContentConfig.title}>
              {node.title}
            </div>
          )}
        </div>
      </div>
    );
  }, [
    expandedKeys,
    showIcon,
    expandIcon,
    collapseIcon,
    handleNodeExpand,
    selectable,
    multiple,
    selectedKeys,
    getCheckState,
    getUpdatedKeysWithCascade,
    handleMultipleSelect,
  ]);

  const listContent = (
    <List
      ref={ref}
      className={cn(
        classConfig.treeConfig({
          virtualScroll: Boolean(virtualScroll),
          infiniteScroll: Boolean(infiniteScroll),
        }),
        'w-full',
        className,
      )}
      virtualScroll={virtualScroll}
      infiniteScroll={infiniteScroll}
      getPositionCache={getPositionCache}
      list={flattenNodes}
    >
      {(nodes) => nodes.map(renderNode)}
    </List>
  );

  if (!selectable) {
    return listContent;
  }

  if (multiple) {
    // 多选模式不使用Checkbox.Group，因为我们需要自定义联动逻辑
    return listContent;
  } else {
    return (
      <RadioGroup
        value={selectedKey}
        onChange={handleSingleSelect}
      >
        {listContent}
      </RadioGroup>
    );
  }
});

Tree.displayName = 'Tree';

export default Tree;
