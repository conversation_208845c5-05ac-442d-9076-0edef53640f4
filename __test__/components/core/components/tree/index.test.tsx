import { createRef } from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import type { TreeRef } from '@/components/core/components/tree';
import Tree from '@/components/core/components/tree';

// Mock List component
vi.mock('@/components/core/components/list', () => {
  return {
    default: ({ children, list, className, ...props }: any) => {
      const items = typeof children === 'function' ? children(list || []) : children;
      return (
        <div className={className} data-testid="tree-container" {...props}>
          {items}
        </div>
      );
    },
  };
});

const mockTreeData = [
  {
    key: '1',
    title: '父节点1',
    children: [
      {
        key: '1-1',
        title: '子节点1-1',
      },
      {
        key: '1-2',
        title: '子节点1-2',
        children: [
          {
            key: '1-2-1',
            title: '子节点1-2-1',
          },
        ],
      },
    ],
  },
  {
    key: '2',
    title: '父节点2',
    children: [
      {
        key: '2-1',
        title: '子节点2-1',
      },
    ],
  },
  {
    key: '3',
    title: '叶子节点',
  },
];

describe('Tree Component', () => {
  it('renders basic tree correctly', () => {
    render(<Tree treeData={mockTreeData} />);
    
    expect(screen.getByText('父节点1')).toBeInTheDocument();
    expect(screen.getByText('父节点2')).toBeInTheDocument();
    expect(screen.getByText('叶子节点')).toBeInTheDocument();
    
    // 子节点默认不显示（未展开）
    expect(screen.queryByText('子节点1-1')).not.toBeInTheDocument();
    expect(screen.queryByText('子节点2-1')).not.toBeInTheDocument();
  });

  it('renders tree with default expanded keys', () => {
    render(
      <Tree 
        treeData={mockTreeData} 
        defaultExpandedKeys={['1', '2']}
      />
    );
    
    expect(screen.getByText('父节点1')).toBeInTheDocument();
    expect(screen.getByText('子节点1-1')).toBeInTheDocument();
    expect(screen.getByText('子节点1-2')).toBeInTheDocument();
    expect(screen.getByText('父节点2')).toBeInTheDocument();
    expect(screen.getByText('子节点2-1')).toBeInTheDocument();
    
    // 深层子节点仍然不显示（未展开）
    expect(screen.queryByText('子节点1-2-1')).not.toBeInTheDocument();
  });

  it('handles node expansion correctly', () => {
    render(<Tree treeData={mockTreeData} />);

    // 初始状态：子节点不可见
    expect(screen.queryByText('子节点1-1')).not.toBeInTheDocument();

    // 点击展开图标 - 查找包含展开图标的div
    const expandIcons = screen.getAllByTestId('expand-icon');
    fireEvent.click(expandIcons[0]); // 点击第一个展开按钮

    // 子节点应该可见
    expect(screen.getByText('子节点1-1')).toBeInTheDocument();
    expect(screen.getByText('子节点1-2')).toBeInTheDocument();
  });

  it('handles node selection correctly', () => {
    const onSelect = vi.fn();
    render(
      <Tree 
        treeData={mockTreeData} 
        onSelect={onSelect}
        defaultExpandedKeys={['1']}
      />
    );
    
    // 点击节点
    fireEvent.click(screen.getByText('子节点1-1'));
    
    expect(onSelect).toHaveBeenCalledWith(
      ['1-1'],
      expect.objectContaining({
        selected: true,
        node: expect.objectContaining({ key: '1-1' }),
      })
    );
  });

  it('handles multiple selection correctly', () => {
    const onSelect = vi.fn();
    render(
      <Tree 
        treeData={mockTreeData} 
        multiple
        onSelect={onSelect}
        defaultExpandedKeys={['1', '2']}
      />
    );
    
    // 选择第一个节点
    fireEvent.click(screen.getByText('子节点1-1'));
    expect(onSelect).toHaveBeenLastCalledWith(
      ['1-1'],
      expect.objectContaining({ selected: true })
    );
    
    // 选择第二个节点
    fireEvent.click(screen.getByText('子节点2-1'));
    expect(onSelect).toHaveBeenLastCalledWith(
      ['1-1', '2-1'],
      expect.objectContaining({ selected: true })
    );
  });

  it('handles disabled nodes correctly', () => {
    const disabledTreeData = [
      {
        key: '1',
        title: '正常节点',
      },
      {
        key: '2',
        title: '禁用节点',
        disabled: true,
      },
    ];
    
    const onSelect = vi.fn();
    render(
      <Tree 
        treeData={disabledTreeData} 
        onSelect={onSelect}
      />
    );
    
    // 点击正常节点
    fireEvent.click(screen.getByText('正常节点'));
    expect(onSelect).toHaveBeenCalled();
    
    // 重置mock
    onSelect.mockClear();
    
    // 点击禁用节点
    fireEvent.click(screen.getByText('禁用节点'));
    expect(onSelect).not.toHaveBeenCalled();
  });

  it('handles controlled mode correctly', () => {
    const onSelect = vi.fn();
    const onExpand = vi.fn();
    
    const { rerender } = render(
      <Tree 
        treeData={mockTreeData}
        selectedKeys={['1-1']}
        expandedKeys={['1']}
        onSelect={onSelect}
        onExpand={onExpand}
      />
    );
    
    // 验证初始状态
    expect(screen.getByText('子节点1-1')).toBeInTheDocument();
    
    // 点击展开另一个节点
    const expandIcons = screen.getAllByTestId('expand-icon');
    fireEvent.click(expandIcons[1]); // 点击第二个展开按钮
    
    expect(onExpand).toHaveBeenCalledWith(
      ['1', '2'],
      expect.objectContaining({ expanded: true })
    );
    
    // 更新props
    rerender(
      <Tree 
        treeData={mockTreeData}
        selectedKeys={['1-1']}
        expandedKeys={['1', '2']}
        onSelect={onSelect}
        onExpand={onExpand}
      />
    );
    
    // 验证新状态
    expect(screen.getByText('子节点2-1')).toBeInTheDocument();
  });

  it('handles custom icons correctly', () => {
    const CustomExpandIcon = () => <span data-testid="custom-expand">+</span>;
    const CustomCollapseIcon = () => <span data-testid="custom-collapse">-</span>;
    
    render(
      <Tree 
        treeData={mockTreeData}
        expandIcon={<CustomExpandIcon />}
        collapseIcon={<CustomCollapseIcon />}
        defaultExpandedKeys={['1']}
      />
    );
    
    expect(screen.getByTestId('custom-collapse')).toBeInTheDocument();
  });

  it('handles showIcon prop correctly', () => {
    render(
      <Tree
        treeData={mockTreeData}
        showIcon={false}
      />
    );

    // 不应该有展开图标
    expect(screen.queryByTestId('expand-icon')).not.toBeInTheDocument();
    expect(screen.queryByTestId('collapse-icon')).not.toBeInTheDocument();
  });

  it('handles onExpand callback correctly', () => {
    const onExpand = vi.fn();
    render(
      <Tree 
        treeData={mockTreeData}
        onExpand={onExpand}
      />
    );
    
    // 点击展开按钮
    const expandIcons = screen.getAllByTestId('expand-icon');
    fireEvent.click(expandIcons[0]);
    
    expect(onExpand).toHaveBeenCalledWith(
      ['1'],
      expect.objectContaining({
        expanded: true,
        node: expect.objectContaining({ key: '1' }),
      })
    );
  });

  it('handles ref correctly', () => {
    const ref = createRef<TreeRef>();
    render(<Tree ref={ref} treeData={mockTreeData} />);
    
    expect(ref.current).toBeDefined();
  });

  it('handles empty tree data', () => {
    render(<Tree treeData={[]} />);
    
    const container = screen.getByTestId('tree-container');
    expect(container).toBeInTheDocument();
    expect(container.children).toHaveLength(0);
  });

  it('handles deep nesting correctly', () => {
    render(
      <Tree 
        treeData={mockTreeData}
        defaultExpandedKeys={['1', '1-2']}
      />
    );
    
    expect(screen.getByText('子节点1-2-1')).toBeInTheDocument();
  });

  it('handles node deselection correctly', () => {
    const onSelect = vi.fn();
    render(
      <Tree 
        treeData={mockTreeData}
        onSelect={onSelect}
        defaultSelectedKeys={['1']}
      />
    );
    
    // 点击已选中的节点进行取消选择
    fireEvent.click(screen.getByText('父节点1'));
    
    expect(onSelect).toHaveBeenCalledWith(
      [],
      expect.objectContaining({ selected: false })
    );
  });

  it('passes through List props correctly', () => {
    render(
      <Tree 
        treeData={mockTreeData}
        virtualScroll={true}
        className="custom-tree-class"
      />
    );
    
    const container = screen.getByTestId('tree-container');
    expect(container).toHaveClass('custom-tree-class');
  });
});
